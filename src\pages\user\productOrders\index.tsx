import { LoadingCircle } from '@/components/_CommonElements/LoadingBar';
import Tabs from '@/components/_CommonElements/Tabs';
import PageContent from '@/components/_PageComponents/PageContent';
import ProductOrders from '@/components/User/ProductOrders';
import { ProfileSectionWrapper } from '@/components/User/ProfileSections';
import { BACKEND } from '@/constants/config';
import { OrderType } from '@/constants/enum/OrderType';
import { EnumTranslationJson } from '@/constants/enum/TranslationJsonKey';
import ENDPOINT from '@/models/api/endpoint';
import { UserProductOrder } from '@/models/api/models/UserProductOrder';
import UserProductOrdersAPIResult from '@/models/api/result/products/order/status';
import i18n from "@/utils/i18n";
import serverSideAuth from '@/utils/serverSideAuth';
import { GetServerSideProps } from 'next';
import { useTranslation } from 'next-i18next';
import { useCallback, useEffect, useMemo, useState } from 'react';

interface GetUserProductOrdersProps {
    type: OrderType;
}
const GetUserProductOrders = ({ type }: GetUserProductOrdersProps) => {   
    const [ pageNum, setPageNum ] = useState(1);
    const [ isLoading, setIsLoading ] = useState<boolean>(false);
    const [ productOrderData, setProductOrderData ] = useState<UserProductOrdersAPIResult>();
    
    const userPaidProductOrdersResponse = useCallback(async (page: number) => {
        setIsLoading(true);
        const res = await (BACKEND.Gateway.fetchQuery<UserProductOrdersAPIResult>({
            url: `${ENDPOINT.GetUserProductOrdersByStatus()}/${type}`,
            query: {
                page: page.toString(),
            },
            params: {
                queryKey: `userPaidOrders_${type}`,
            },
        }));
        setProductOrderData(res);
        setIsLoading(false);
    }, []);


    const goToPage = useCallback((page: number) => {
        if (page != pageNum) {
            userPaidProductOrdersResponse(page);
            setPageNum(page);
        }
    }, [pageNum]);

    useEffect(() => {
        if (!productOrderData) {
            userPaidProductOrdersResponse(pageNum);
        }
    }, [pageNum]);


    const orders = productOrderData?.data;
    const maxPageNum = useMemo(() => {
        const normalOrderMaxPage = orders?.maxPage || 0;
        return normalOrderMaxPage;
    }, [orders?.maxPage]);

    const orderList: UserProductOrder[] = orders?.list ?? [];

    return (
        <>
            {isLoading ? 
            <LoadingCircle /> :
            <ProductOrders
                userProductOrder={orderList}
                listType={type}
                pageNum={pageNum}
                maxPageNum={maxPageNum}
                setPageNum={goToPage}
            />}
        </>
    );
}

const UserProductOrdersPage = () => {
    const { t: seoTranslation } = useTranslation(EnumTranslationJson.SEO);
    const { t: profileTranslation } = useTranslation(EnumTranslationJson.Profile);
    const tabs = useMemo(() => [
        {
            name: profileTranslation("order.completed"),
            contents: <GetUserProductOrders type={OrderType.COMPLETED} />,
        },
        {
            name: profileTranslation("order.processing"),
            contents: <GetUserProductOrders type={OrderType.PROCESSING} />
        },
        {
            name: profileTranslation("order.paid"),
            contents: <GetUserProductOrders type={OrderType.PAID} />,
            active: true
        },
        {
            name: profileTranslation("order.pending"),
            contents: <GetUserProductOrders type={OrderType.PENDING} />
        },
        {
            name: profileTranslation("order.expired"),
            contents: <GetUserProductOrders type={OrderType.EXPIRED} />
        }
    ], [profileTranslation]);
    const title = useMemo(() => seoTranslation("page.productOrders.title"), [seoTranslation]);
    return (
        <PageContent
            title={title}
            content={
                <ProfileSectionWrapper>
                    <Tabs tabs={tabs} />
                </ProfileSectionWrapper>
            }
        />
    );
};

export const getServerSideProps: GetServerSideProps = serverSideAuth(
    {
        permission: "userOnly",
    },
    (context) =>
        i18n.GetServerSidePropsAsync({
            additionalFiles: [EnumTranslationJson.Account, EnumTranslationJson.Profile],
            context,
    })
);
export default UserProductOrdersPage;
