title: Profile
myProfile: My Profile
boundTickets:
  title: Ticket(s)
  qrCode:
    button: Show QR Code
unboundTickets: Unbound Ticket(s)
membershipSubscription:
  title: Membership
  hasNoSubscribed: Haven't joined yet? Choose a plan to have more fun.
  duration: "{{DURATION}} MONTHS"
  subscribedDate: "Start from: {{START_DATE_TIME}}"
  validTill: "Expires on: {{VALID_TILL}}"
  expiryDateTime: "This QR Code will expiry on: {{EXP_DATETIME}}"
  buttons:
    cancel: Unsubscribe
    join: JOIN
  dialog:
    common: 
      buttons:
        cancel: Cancel
        confirm: Confirm
    purchase:
      title: "Purchase {{MEMBERSHIP_TYPE_NAME}}"
      agreementClaim: | 
        I have read and agreed to <u>Terms & Conditions</u> and <u>Privacy Policy</u>.
    requiredFields:
      title: CAUTION
      body: Please complete user information before become a member
      buttons:
        goToComplete: Go and Complete
membershipReservation:
  title: Reservation
redeem: Redeem Ticket
productOrders: Product Order(s)
ticketOrders: Ticket Order(s)
profile:
  name: Name
  email: Email Address
  firstName: First Name
  lastName: Last Name
  dateOfBirth: Date Of Birth
  mobile: Phone Number
  country: Current country of residence
  promotionOptIn: Receive promotional content
  changePassword: Change Password
  verifyEmail: Verify Email
  update: Update
event:
  active: Current
  end: Ended 
  upComing: Upcoming
  boundEvent: Event Bound
  unBoundEvent: Event Not Bound
  showTicket: My Redemption Code
  hideTicket: Hide Redemption Code
  shareTicket:
    button: Share
  redeemTicket: 
    button: Redeem
order:
  completed: Delivered
  paid: Paid
  processing: Processing
  pending: Pending
  expired: Expired
  reminder: The admission QR can be found on "Ticket(s)" under "My Profile". Also, a separate email with admission QR code should be sent to your registered email.
  detail:
    orderNo:  "Order #{{orderId}}"
    date: "Date: {{date}}"
    item:
      eventName: Event
      name: "Name"
      quantity: "Quantity"
      unitPrice: "Unit Price"
      subtotal: "Subtotal"
      discount: "Applied discount:"
      grandTotal: "Grand Total"
    label:
      product: "Product"
      ticket: "Ticket"
    viewDetails: View Details
generateQrCode: Click to generate QRCODE