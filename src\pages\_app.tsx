import FbPixel from '@/components/_PageComponents/FbPixel';
//Custom class - components
import DefaultMeta from "@/components/_PageComponents/Meta/Default";
import Layouts from "@/components/Layouts";
// redux
import { persistor, store } from "@/redux/store";
import createEmotionCache from "@/styles/mui/createEmotionCache";
import MuiTheme from "@/styles/mui/theme";
import { CacheProvider, EmotionCache } from "@emotion/react";
//Material UI
import { AppRouterCacheProvider } from "@mui/material-nextjs/v14-appRouter";
import CssBaseline from "@mui/material/CssBaseline";
import GlobalStyles from "@mui/material/GlobalStyles";
import { ThemeProvider } from "@mui/material/styles";
// stoneleigh/api
import { APIConfig, DehydratedState, GenericAPI } from "@stoneleigh/api-lib";
//fontawesome icon
import { loadCSS } from 'fg-loadcss';
import * as ga from "lib/ga";
//Next.js
import { AppProps } from "next/app";
import { useRouter } from "next/router";
// ts
import { encode, ParsedUrlQuery } from "querystring";
//React
import * as React from "react";
import { useEffect } from "react";
import { Provider as ReduxProvider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";

//Custom class - styles
import "@/styles/globals/_index.scss";
import "@/styles/utils/phoneInput.scss";
//for PhoneInput components custom MUI & flags styles
import "@stoneleigh/navigation-menu/dist/menu.css";
import "react-image-gallery/styles/scss/image-gallery.scss";

// Client-side cache, shared for the whole session of the user in the browser.
const clientSideEmotionCache = createEmotionCache();

// i18n
import { appWithTranslation } from "next-i18next";
import EnumRequestHeader from "@/constants/enum/RequestHeader";
import { EnumLocale } from "@/constants/enum/Locale";

//datepicker
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'

// Snackbar
import { SnackbarProvider } from 'notistack'

import DayJSPlugin_Timezone from 'dayjs/plugin/timezone';
import DayJSPlugin_UTC from 'dayjs/plugin/utc';
import DayJSPlugin_LocalizedFormat from 'dayjs/plugin/localizedFormat';
import 'dayjs/locale/en';
import 'dayjs/locale/zh-hk';
import 'dayjs/locale/th';
import dayjs from "dayjs";
import API from "@/utils/API";
import { mapToDayjsLocale } from "@/utils/dayjs";
import { EnumCookieKey } from "@/constants/enum/Cookies";
import Grow from "@mui/material/Grow";
import PageEssentials from "@/components/_PageComponents/PageEssentials";
import { useFlutterInAppWebViewPlatformReadyState } from "@/hooks/Webview/FlutterInAppWebViewPlatformReadyState";

APIConfig.Init({
    internal: new GenericAPI()
});

dayjs.extend(DayJSPlugin_UTC);
dayjs.extend(DayJSPlugin_Timezone);
dayjs.extend(DayJSPlugin_LocalizedFormat);

interface HandleFromSourceProps {
    query: ParsedUrlQuery;
    cookies?: { [key in string]: string };
}
const handleFromSource = (props: HandleFromSourceProps) => {
    const { query, cookies } = props;
    const searchParams = new URLSearchParams(encode(query));
    // get secret from query
    let fromSource = "";
    if (searchParams.size > 0) {
        fromSource = searchParams.get("s") ?? "";
    }
    // get secret from cookie
    let newCookies: { [key in string]: string } = cookies ?? {};
    if (cookies && fromSource !== "") {
        const cookie = cookies[EnumCookieKey.TRACED_REFERRAL_CODE] ?? "";
        if (cookie === "") {
            newCookies = {
                ...cookies,
                [EnumCookieKey.TRACED_REFERRAL_CODE]: fromSource
            };
            API.SetCookie(EnumCookieKey.TRACED_REFERRAL_CODE, fromSource);
        }
    }
    return newCookies;
};

interface CustomPageProps {
    cookies?: { [key in string]: string },
    dehydratedState: DehydratedState
}
interface MyAppProps extends AppProps<CustomPageProps> {
    userJWT?: string;
    emotionCache?: EmotionCache;
}
const MyApp = ({Component, emotionCache = clientSideEmotionCache, pageProps}: MyAppProps) => {
    const router = useRouter();

    const { locale, query } = router;
    dayjs.locale(mapToDayjsLocale(locale || 'en-US'));

    const QueryClient = APIConfig.CreateQueryClient();
    APIConfig.SetQueryClient(QueryClient);

    const flutterInAppWebViewPlatform = useFlutterInAppWebViewPlatformReadyState();
    flutterInAppWebViewPlatform.useAddListener();

    useEffect(() => {
        const node = loadCSS(
            "https://site-assets.fontawesome.com/releases/v6.4.2/css/all.css",
            // Inject before JSS
            (document.querySelector('#font-awesome-css') || document.head.firstChild) as HTMLElement,
          );
      
          return () => {
            node.parentNode!.removeChild(node);
          };
    }, []);
    useEffect(() => {

        const handleRouteChange = (url: string) => {
            ga.pageview(url);
            // trySwitchAppMode();
        };

        // trySwitchAppMode();

        router.events.on("routeChangeComplete", handleRouteChange);

        return () => {
            router.events.off("routeChangeComplete", handleRouteChange);
        };
    }, [router.events]);
    useEffect(() => {
        if (typeof window !== 'undefined') {
            handleFromSource({query, cookies: pageProps.cookies});
        }
    }, [pageProps.cookies, query]);

    if (pageProps && pageProps.cookies) {
        if (typeof window === 'undefined') {
            API.SetServerSideCookies(pageProps.cookies);
        }
    }
    return (
        <ReduxProvider store={store}>
            <PersistGate loading={null} persistor={persistor}>
                {() => (
                    <>
                        <AppRouterCacheProvider>
                            <DefaultMeta />
                            <FbPixel />

                            <ThemeProvider theme={MuiTheme}>
                                {/* CssBaseline kickstart an elegant, consistent, and simple baseline to build upon. */}
                                <CssBaseline />
                                <GlobalStyles
                                    styles={{ body: { backgroundColor: "rgba(18, 18, 18, 1)" } }}
                                />
                                <APIConfig.APIConfigProvider
                                    queryClient={QueryClient}
                                    dehydratedState={pageProps.dehydratedState}
                                    gatewayConfigs={{
                                        internal: {
                                            headers: [
                                                {
                                                    key: EnumRequestHeader.AUTHORIZATION,
                                                    value: () => {
                                                        return API.GetUserJWT() || "";
                                                    },
                                                },
                                                {
                                                    key: EnumRequestHeader.LANGUAGE,
                                                    value: (router.locale || EnumLocale.English),
                                                },
                                            ]
                                        }
                                    }}
                                >
                                    <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale={mapToDayjsLocale(router.locale || 'en-US')}>
                                        <SnackbarProvider
                                            maxSnack={3}
                                            autoHideDuration={5000}
                                            anchorOrigin={{
                                                vertical: 'bottom',
                                                horizontal: 'center',
                                            }}
                                            TransitionComponent={Grow}
                                            classes={{containerRoot: "z-alert"}}
                                        >
                                            <Layouts>
                                                <Component {...pageProps} />
                                                <PageEssentials />
                                            </Layouts>
                                        </SnackbarProvider>
                                    </LocalizationProvider>

                                </APIConfig.APIConfigProvider>
                            </ThemeProvider>
                        </AppRouterCacheProvider>
                    </>
                )}
            </PersistGate>
        </ReduxProvider>
    );
};
export default appWithTranslation(MyApp);
