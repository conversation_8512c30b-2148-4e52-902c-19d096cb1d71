/**
 * Utility functions for dayjs locale mapping
 */

/**
 * Map Next.js locale format to dayjs locale format
 * @param locale Next.js locale (e.g., 'th-TH', 'en-US', 'zh-HK')
 * @returns dayjs locale (e.g., 'th', 'en', 'zh-hk')
 */
export const mapToDayjsLocale = (locale: string): string => {
    switch (locale) {
        case 'th-TH':
            return 'th';
        case 'zh-HK':
            return 'zh-hk';
        case 'en-US':
            return 'en';
        default:
            return 'en'; // fallback to English
    }
};
